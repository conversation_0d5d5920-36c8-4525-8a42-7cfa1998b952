from autogen_agentchat.agents import AssistantAgent
from autogen_ext.models.openai import OpenAIChatCompletionClient
from dotenv import load_dotenv
import os

load_dotenv()

api_key = os.getenv('OPENAI_API_KEY')

model_client = OpenAIChatCompletionClient(model='gpt-4o',api_key=api_key)


agent_1 = AssistantAgent(name='my_assistant',model_client=model_client)

result = await agent_1.run(task='Tell me something about you?')

print(result.messages[-1].content)

result2 = await agent_1.run(task='What was my last question?')

print(result2.messages[-1].content)