{"cells": [{"cell_type": "markdown", "id": "9f92850c", "metadata": {}, "source": ["# Autogen Installation\n"]}, {"cell_type": "markdown", "id": "b1a10e41", "metadata": {}, "source": ["1. Create a venv\n", "2. Create a requirements.txt\n", "3. install autogen-agentchat autogen-core autogen-ext"]}, {"cell_type": "markdown", "id": "4067dc4c", "metadata": {}, "source": ["# First Autogen Agent\n"]}, {"cell_type": "code", "execution_count": 1, "id": "688ca4fa", "metadata": {}, "outputs": [], "source": ["from autogen_agentchat.agents import AssistantAgent\n", "from autogen_ext.models.openai import OpenAIChatCompletionClient\n", "from dotenv import load_dotenv\n", "import os\n", "\n", "load_dotenv()\n", "\n", "api_key = os.getenv('OPENAI_API_KEY')"]}, {"cell_type": "markdown", "id": "5933fdac", "metadata": {}, "source": ["# Connecting to the Model"]}, {"cell_type": "code", "execution_count": 2, "id": "93e8ce47", "metadata": {}, "outputs": [], "source": ["model_client = OpenAIChatCompletionClient(model='gpt-4o',api_key=api_key)\n"]}, {"cell_type": "code", "execution_count": 3, "id": "7237<PERSON><PERSON>", "metadata": {}, "outputs": [], "source": ["agent_1 = AssistantAgent(name='my_assistant',model_client=model_client)"]}, {"cell_type": "code", "execution_count": 10, "id": "68fd927d", "metadata": {}, "outputs": [], "source": ["result = await agent_1.run(task='Tell me something about you?')"]}, {"cell_type": "code", "execution_count": 9, "id": "647be0d9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["I am an AI assistant designed to help answer questions, provide information, and assist with a variety of tasks to the best of my knowledge and abilities. My training includes a wide range of topics up until October 2023, and I can aid in research, problem-solving, and general queries. My goal is to assist users effectively and efficiently. How can I assist you today?\n"]}], "source": ["print(result.messages[-1].content)"]}, {"cell_type": "code", "execution_count": 11, "id": "484d9d3f", "metadata": {}, "outputs": [], "source": ["result2 = await agent_1.run(task='What was my last question?')"]}, {"cell_type": "code", "execution_count": 12, "id": "b213a370", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Your last question was, \"Tell me something about you?\" If there's anything else you'd like to know or explore, please let me know!\n"]}], "source": ["print(result2.messages[-1].content)"]}, {"cell_type": "code", "execution_count": null, "id": "bf6de196", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "autogen-venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}